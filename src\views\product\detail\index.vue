<template>
  <div class="product-detail">
    <ElCard shadow="never">
      <template #header>
        <div class="card-header">
          <span>产品详情</span>
          <ElButton @click="goBack">返回列表</ElButton>
        </div>
      </template>
      
      <ElForm :model="productForm" label-width="120px">
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="产品名称">
              <ElInput v-model="productForm.name" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品分类">
              <ElSelect v-model="productForm.category" placeholder="请选择分类">
                <ElOption label="手机" value="手机" />
                <ElOption label="电脑" value="电脑" />
                <ElOption label="平板" value="平板" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>
        
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="价格">
              <ElInput v-model="productForm.price" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="状态">
              <ElRadioGroup v-model="productForm.status">
                <ElRadio value="上架">上架</ElRadio>
                <ElRadio value="下架">下架</ElRadio>
              </ElRadioGroup>
            </ElFormItem>
          </ElCol>
        </ElRow>
        
        <ElFormItem label="产品描述">
          <ElInput 
            v-model="productForm.description" 
            type="textarea" 
            :rows="4"
            placeholder="请输入产品描述"
          />
        </ElFormItem>
        
        <ElFormItem>
          <ElButton type="primary" @click="handleSave">保存</ElButton>
          <ElButton @click="goBack">取消</ElButton>
        </ElFormItem>
      </ElForm>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'ProductDetail' })

const router = useRouter()

const productForm = ref({
  name: '',
  category: '',
  price: '',
  status: '上架',
  description: ''
})

const handleSave = () => {
  ElMessage.success('保存成功')
}

const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.product-detail {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
