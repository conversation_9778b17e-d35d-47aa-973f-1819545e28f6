# Art Design Pro 技术文档

本文档旨在总结 Art Design Pro 项目的 UI 风格、配色方案和技术实现，为后续的二次开发提供参考。

## 1. 技术栈

项目基于以下技术构建：

- **Vue 3**: 渐进式 JavaScript 框架。
- **Vite**: 新一代前端构建工具。
- **Element Plus**: 基于 Vue 3 的组件库。
- **Sass**: CSS 预处理器，用于编写更强大、更易于维护的样式。
- **Pinia**: Vue 的状态管理库。

## 2. 设计系统

项目的视觉设计系统由以下核心元素构成：

### 2.1. 颜色系统

项目定义了一套完整的颜色系统，并支持亮色（Light）和暗色（Dark）两种主题模式。颜色变量通过 CSS 自定义属性（CSS Variables）在 `:root` 和 `html.dark` 选择器中定义，实现了动态主题切换。

#### 2.1.1. 主题色

| 类别 | 变量名 | 亮色模式 (RGB) | 暗色模式 (RGB) |
| :--- | :--- | :--- | :--- |
| 主要 | `--art-primary` | `93, 135, 255` | `93, 135, 255` |
| 次要 | `--art-secondary` | `73, 190, 255` | `73, 190, 255` |
| 成功 | `--art-success` | `19, 222, 185` | `19, 222, 185` |
| 警告 | `--art-warning` | `255, 174, 31` | `255, 174, 31` |
| 错误 | `--art-error` | `250, 137, 107` | `250, 137, 107` |
| 信息 | `--art-info` | `107, 125, 155` | `107, 125, 155` |
| 危险 | `--art-danger` | `255, 77, 79` | `255, 77, 79` |

#### 2.1.2. 背景色

背景色同样分为亮色和暗色两种模式，为不同状态提供了视觉反馈。

- **亮色模式**:
  - `--art-bg-color`: `#fafbfc` (最底层背景)
  - `--art-main-bg-color`: `#ffffff` (主内容区背景)
  - `--art-bg-primary`: `236, 242, 255`
- **暗色模式**:
  - `--art-bg-color`: `#070707`
  - `--art-main-bg-color`: `#161618`
  - `--art-bg-primary`: `37, 54, 98`

#### 2.1.3. 灰色系

灰色系用于文本、边框和背景，提供了丰富的层次感。

| 变量名 | 亮色模式 | 暗色模式 |
| :--- | :--- | :--- |
| `--art-gray-100` | `#f9f9f9` | `#1b1c22` |
| `--art-gray-500` | `#99a1b7` | `#636674` |
| `--art-gray-900` | `#071437` | `#f5f5f5` |

### 2.2. 字体

项目引入了两种自定义字体：

- **DMSans**: 用于常规文本，风格现代、易读。
- **Montserrat**: 用于标题或特殊文本，具有几何美感。

### 2.3. 布局和间距

- **响应式设计**: 使用媒体查询来适应不同设备尺寸，如笔记本、平板和手机。
- **卡片式布局**: 页面内容多采用卡片（`.page-content`, `.art-custom-card`）进行组织，使布局清晰、模块化。
- **Flexbox**: 广泛使用 Flexbox 进行元素对齐和分布。

### 2.4. 阴影和边框

项目定义了两种盒子模式，可以通过 `data-box-mode` 属性进行切换：

- **边框模式 (`border-mode`)**:
  - 卡片和侧边栏使用由 `--art-card-border` 变量定义的边框。
- **阴影模式 (`shadow-mode`)**:
  - 卡片使用 `box-shadow` 创造悬浮感，同时保留了较浅的边框。

阴影效果分为多个层级，如 `--art-box-shadow-xs`, `--art-box-shadow-sm`, `--art-box-shadow`, `--art-box-shadow-lg`，为不同组件提供了丰富的立体感。

## 3. 组件样式

### 3.1. 按钮

- **图标按钮 (`.btn-icon`)**: 具有特定的字体大小。
- **红色按钮 (`.el-btn-red`)**: 定义了特殊的红色文本，并带有悬停和点击效果。

### 3.2. 卡片

- **表格卡片 (`.art-table-card`)**: 专为表格设计的卡片，具有圆角和 flex 布局，使其内容能够自适应高度。
- **自定义卡片 (`.art-custom-card`)**: 通用卡片样式，边框和阴影可根据盒子模式变化。

### 3.3. 徽章

- **点状徽章 (`.art-badge`)**: 用于提示，带有呼吸动画效果，吸引用户注意。
- **文字徽章 (`.art-text-badge`)**: 用于显示数字或少量文本，如消息计数。

## 4. 主题切换

主题切换的核心在于 `variables.scss` 文件。

- **CSS 变量**: 所有颜色相关的样式都使用 CSS 变量。
- **亮色主题**: 默认在 `:root` 中定义亮色主题的变量。
- **暗色主题**: 当 `<html>` 元素添加 `.dark` 类时，`html.dark` 选择器下的变量会覆盖 `:root` 中的同名变量，从而实现暗色主题的切换。

这种方法利用了 CSS 的级联和继承特性，使得主题切换高效且易于维护。开发者只需更新 CSS 变量，即可在整个应用中改变颜色，而无需修改组件的样式代码。

## 5. 总结

Art Design Pro 的设计风格现代、简洁，具有高度的可定制性。其出色的 UI/UX 体现在以下几个方面：

- **一致性**: 通过全局变量和组件化样式，确保了整个应用视觉风格的一致性。
- **响应式**: 适配多种设备，提供了良好的跨平台体验。
- **用户反馈**: 丰富的悬停效果、过渡动画和状态颜色，为用户操作提供了及时的视觉反馈。
- **可访问性**: 提供了色弱模式，增强了应用的可访问性。

这份文档为二次开发提供了全面的设计参考。开发者可以遵循本文档中总结的设计规范，以保持项目风格的统一，并高效地进行新功能的开发。
