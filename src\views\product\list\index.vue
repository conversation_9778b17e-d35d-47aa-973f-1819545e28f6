<template>
  <div class="product-list">
    <ElCard class="art-table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>产品列表</span>
          <ElButton type="primary" @click="handleAdd">
            <ElIcon><Plus /></ElIcon>
            新增产品
          </ElButton>
        </div>
      </template>
      
      <ElTable :data="productList" style="width: 100%">
        <ElTableColumn prop="id" label="ID" width="80" />
        <ElTableColumn prop="name" label="产品名称" />
        <ElTableColumn prop="category" label="分类" />
        <ElTableColumn prop="price" label="价格" />
        <ElTableColumn prop="status" label="状态">
          <template #default="{ row }">
            <ElTag :type="row.status === '上架' ? 'success' : 'danger'">
              {{ row.status }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="200">
          <template #default="{ row }">
            <ElButton size="small" @click="handleEdit(row)">编辑</ElButton>
            <ElButton size="small" type="danger" @click="handleDelete(row)">删除</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

defineOptions({ name: 'ProductList' })

// 模拟数据
const productList = ref([
  { id: 1, name: 'iPhone 15', category: '手机', price: '¥5999', status: '上架' },
  { id: 2, name: 'MacBook Pro', category: '电脑', price: '¥12999', status: '上架' },
  { id: 3, name: 'iPad Air', category: '平板', price: '¥3999', status: '下架' },
])

const handleAdd = () => {
  ElMessage.success('新增产品功能')
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑产品: ${row.name}`)
}

const handleDelete = (row: any) => {
  ElMessage.warning(`删除产品: ${row.name}`)
}
</script>

<style lang="scss" scoped>
.product-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
